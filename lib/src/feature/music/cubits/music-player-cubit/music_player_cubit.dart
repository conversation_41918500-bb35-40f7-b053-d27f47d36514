import 'dart:io';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:shridattmandir/src/core/core.dart'
    show CubitExt, SdmSecureStorage, SdmUrls;
import 'package:shridattmandir/src/feature/feature.dart'
    show LoopModeEnum, MusicModel, PlayerButtonState;
import 'package:shridattmandir/src/feature/music/models/index_enum.dart';
import 'package:shridattmandir/src/feature/music/repository/music_repository.dart';
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

part 'music_player_state.dart';

class MusicPlayerCubit extends Cubit<MusicPlayerState> {
  MusicPlayerCubit({
    required AudioPlayer audioPlayer,
    required MusicRepository musicRepository,
  })  : _audioPlayer = audioPlayer,
        _musicRepository = musicRepository,
        super(const MusicPlayerState());

  late final AudioPlayer _audioPlayer;
  final MusicRepository _musicRepository;

  Future<void> clearCache() async {
    try {
      final musicCacheClearedDateTime = await SdmSecureStorage.readString(
        key: SdmSecureStorage.keyMusicCacheClearedDateTime,
      );

      if (musicCacheClearedDateTime != null) {
        final musicCacheClearedDate = DateTime.tryParse(
          musicCacheClearedDateTime,
        );

        if (musicCacheClearedDate == null) {
          await SdmSecureStorage.writeString(
            key: SdmSecureStorage.keyMusicCacheClearedDateTime,
            value: DateTime.now().toIso8601String(),
          );
          return;
        }

        final difference = DateTime.now().difference(
          musicCacheClearedDate,
        );

        if (difference.inDays >= 3) {
          await AudioPlayer.clearAssetCache();
          await SdmSecureStorage.writeString(
            key: SdmSecureStorage.keyMusicCacheClearedDateTime,
            value: DateTime.now().toIso8601String(),
          );
        }
      } else {
        await SdmSecureStorage.writeString(
          key: SdmSecureStorage.keyMusicCacheClearedDateTime,
          value: DateTime.now().toIso8601String(),
        );
      }
    } catch (e, s) {
      if (e is PathNotFoundException) return;

      SdmToast.show(
        e.toString(),
      );

      FirebaseCrashlytics.instance.recordFlutterError(
        FlutterErrorDetails(
          exception: e,
          stack: s,
        ),
      );
    }
  }

  Future<void> initializeAudioPlayer({
    List<MusicModel>? musicList,
    int index = 0,
    DocumentSnapshot? lastVisibleDocumentSnapshot,
    IndexEnum? indexEnum,
    int limit = 10,
  }) async {
    safeEmit(
      state.copyWith(
        autoPlayLimit: limit,
      ),
    );

    if (musicList == null) {
      safeEmit(
        state.copyWith(
          currentMusic: () => state.currentMusic,
        ),
      );
      return;
    }

    if (state.currentMusic?.documentId == musicList[index].documentId) {
      return;
    }

    safeEmit(
      state.copyWith(
        playlistChildren: musicList,
        lastVisibleDocumentSnapshot: lastVisibleDocumentSnapshot,
        indexEnum: indexEnum,
        currentMusic: () => musicList[index],
        playlistCurrentIndex: index,
        initialStartIndex: index, // Track the initial start index
      ),
    );

    try {
      // Initialize playlist with multiple songs for better performance
      await _initializePlaylistWithPreloading(musicList, index);

      await switchLoopMode(
        loopMode: LoopModeEnum.once,
      );

      safeEmit(
        state.copyWith(
          playlistCurrentIndex: index,
          currentMusic: () => musicList[index],
        ),
      );

      await play();

      // Log playlist state after initialization
      logCurrentPlaylistItems();
    } catch (e) {
      SdmToast.show(
        e.toString(),
      );
    }
  }

  /// Initialize playlist with preloading of 4-5 songs for optimal performance
  Future<void> _initializePlaylistWithPreloading(
    List<MusicModel> musicList,
    int startIndex,
  ) async {
    const int preloadCount = 5; // Number of songs to preload
    final List<AudioSource> audioSources = [];

    // Calculate how many songs we can preload
    final int songsToLoad = math.min(
      preloadCount,
      musicList.length - startIndex,
    );

    // Create audio sources for the initial batch
    for (int i = 0; i < songsToLoad; i++) {
      final musicIndex = startIndex + i;
      final music = musicList[musicIndex];

      audioSources.add(
        LockCachingAudioSource(
          Uri.parse(music.audioUrl ?? ''),
          tag: MediaItem(
            id: music.documentId ?? math.Random().nextInt(500).toString(),
            title: music.title ?? '',
            artist: music.lyricist ?? '',
            artUri: Uri.parse(
              (music.artUrl ?? '').isEmpty
                  ? SdmUrls.kMandirLogo
                  : music.artUrl!,
            ),
          ),
        ),
      );
    }

    // Set the playlist for better performance
    await _audioPlayer.setAudioSources(
      audioSources,
      initialIndex: 0, // Always start at the first song in our preloaded batch
      initialPosition: Duration.zero,
    );
  }

  Future<void> fetchMusic() async {
    final musicList = await _musicRepository.fetchMusic(
      documentSnapshot: state.lastVisibleDocumentSnapshot,
    );

    if (musicList.musicModel.isEmpty) {
      return;
    }

    safeEmit(
      state.copyWith(
        playlistChildren: [
          ...state.playlistChildren ?? [],
          ...musicList.musicModel,
        ],
        lastVisibleDocumentSnapshot: musicList.lastVisibleDocumentSnapshot,
      ),
    );
  }

  Future<void> addNextSongToQueue() async {
    try {
      // Fetch more music if we're running low
      if ((state.playlistChildren?.length ?? 0) <=
          (state.playlistCurrentIndex + 2)) {
        await fetchMusic();
      }

      await _maintainPlaylistQueue();
    } catch (e) {
      SdmToast.show(
        e.toString(),
      );
    }
  }

  /// Safely maintains the playlist queue without blocking the UI
  void _maintainPlaylistQueueSafely() {
    // Run queue maintenance in the background without blocking
    _maintainPlaylistQueue().catchError((error) {
      // Silently handle errors to avoid disrupting playback
      debugPrint('Queue maintenance error: $error');
    });
  }

  /// Maintains the playlist queue to ensure 4-5 songs are always ready
  Future<void> _maintainPlaylistQueue() async {
    try {
      const int targetQueueSize = 5;
      final int currentAudioSourcesCount = _audioPlayer.sequence.length;
      final int currentPlaylistIndex = state.playlistCurrentIndex;
      final List<MusicModel> playlistChildren = state.playlistChildren ?? [];

      // Update queue size in state
      safeEmit(
        state.copyWith(
          queueSize: currentAudioSourcesCount,
        ),
      );

      // Safety check: ensure we have a valid playlist
      if (playlistChildren.isEmpty || currentPlaylistIndex < 0) {
        return;
      }

      // Calculate how many more songs we need in the audio player queue
      final int remainingSongsInQueue =
          currentAudioSourcesCount - (_audioPlayer.currentIndex ?? 0) - 1;
      final int songsNeededInQueue = targetQueueSize - remainingSongsInQueue;

      if (songsNeededInQueue <= 0) return;

      // Find the next songs to add to the queue
      // Calculate the next song index based on the initial start index and current audio sources
      final int initialStartIndex = _getInitialStartIndex();
      final int nextSongIndex = initialStartIndex + currentAudioSourcesCount;

      // Safety check: ensure we don't go out of bounds
      if (nextSongIndex >= playlistChildren.length) {
        return;
      }

      final int songsToAdd = math.min(
        songsNeededInQueue,
        playlistChildren.length - nextSongIndex,
      );

      // Add songs to the queue
      for (int i = 0; i < songsToAdd; i++) {
        final songIndex = nextSongIndex + i;
        if (songIndex >= 0 && songIndex < playlistChildren.length) {
          final music = playlistChildren[songIndex];

          await _audioPlayer.addAudioSource(
            LockCachingAudioSource(
              Uri.parse(music.audioUrl ?? ''),
              tag: MediaItem(
                id: music.documentId ?? math.Random().nextInt(500).toString(),
                title: music.title ?? '',
                artist: music.lyricist ?? '',
                artUri: Uri.parse(
                  (music.artUrl ?? '').isEmpty
                      ? SdmUrls.kMandirLogo
                      : music.artUrl!,
                ),
              ),
            ),
          );
        }
      }

      // Update queue size after adding songs
      final int updatedAudioSourcesCount = _audioPlayer.sequence.length;
      safeEmit(
        state.copyWith(
          queueSize: updatedAudioSourcesCount,
        ),
      );

      // Log queue maintenance if songs were added
      if (songsToAdd > 0) {
        debugPrint('🔄 Queue maintenance: Added $songsToAdd songs to queue');
        debugPrint(
            '📊 Queue size: $currentAudioSourcesCount → $updatedAudioSourcesCount');
      }
    } catch (e) {
      SdmToast.show(
        'Error maintaining playlist queue: ${e.toString()}',
      );
    }
  }

  void updateCurrentMusic() {
    final currentAudioPlayerIndex = _audioPlayer.currentIndex ?? 0;
    final List<MusicModel> playlistChildren = state.playlistChildren ?? [];

    // Safety check: ensure we have a valid playlist
    if (playlistChildren.isEmpty) {
      return;
    }

    // Calculate the actual music index based on the initial start index
    final actualMusicIndex = _getActualMusicIndex(currentAudioPlayerIndex);

    // Safety check: ensure the calculated index is within bounds
    if (actualMusicIndex < 0 || actualMusicIndex >= playlistChildren.length) {
      return;
    }

    // Only update if the index has actually changed
    if (actualMusicIndex == state.playlistCurrentIndex) {
      return;
    }

    safeEmit(
      state.copyWith(
        playlistCurrentIndex: actualMusicIndex,
        currentMusic: () => playlistChildren[actualMusicIndex],
      ),
    );

    // Maintain the queue when the current song changes (but don't await to avoid blocking)
    _maintainPlaylistQueueSafely();
  }

  /// Gets the actual music index from the audio player index
  int _getActualMusicIndex(int audioPlayerIndex) {
    // The audio player index directly corresponds to the position in our preloaded queue
    // Since we initialize the audio player starting from the selected song,
    // the actual music index is the initial index plus the audio player's current position
    final initialStartIndex = _getInitialStartIndex();
    return initialStartIndex + audioPlayerIndex;
  }

  /// Gets the initial start index that was used when initializing the audio player
  int _getInitialStartIndex() {
    // Use the tracked initial start index from state
    return state.initialStartIndex >= 0 ? state.initialStartIndex : 0;
  }

  /// Gets current playlist information for debugging and UI purposes
  Map<String, dynamic> getPlaylistInfo() {
    return {
      'audioPlayerSequenceLength': _audioPlayer.sequence.length,
      'audioPlayerCurrentIndex': _audioPlayer.currentIndex ?? 0,
      'statePlaylistLength': state.playlistChildren?.length ?? 0,
      'stateCurrentIndex': state.playlistCurrentIndex,
      'initialStartIndex': state.initialStartIndex,
      'hasNext': _audioPlayer.hasNext,
      'hasPrevious': _audioPlayer.hasPrevious,
      'currentSongTitle': state.currentMusic?.title ?? 'Unknown',
      'queueSize': state.queueSize,
    };
  }

  /// Logs the current playlist items for debugging
  void logCurrentPlaylistItems() {
    debugPrint('\n=== MUSIC PLAYER PLAYLIST DEBUG ===');
    debugPrint('Timestamp: ${DateTime.now()}');

    // Basic info
    final info = getPlaylistInfo();
    debugPrint('📊 Basic Info:');
    info.forEach((key, value) {
      debugPrint('  $key: $value');
    });

    // Audio Player Sequence
    debugPrint(
        '\n🎵 Audio Player Sequence (${_audioPlayer.sequence.length} items):');
    for (int i = 0; i < _audioPlayer.sequence.length; i++) {
      final item = _audioPlayer.sequence[i];
      final isCurrentlyPlaying = i == (_audioPlayer.currentIndex ?? -1);
      final marker = isCurrentlyPlaying ? '▶️' : '  ';

      if (item.tag is MediaItem) {
        final mediaItem = item.tag as MediaItem;
        debugPrint('$marker [$i] ${mediaItem.title} - ${mediaItem.artist}');
      } else {
        debugPrint('$marker [$i] Unknown item');
      }
    }

    // State Playlist
    final playlistChildren = state.playlistChildren ?? [];
    debugPrint('\n📋 State Playlist (${playlistChildren.length} items):');
    for (int i = 0; i < playlistChildren.length; i++) {
      final music = playlistChildren[i];
      final isCurrentlySelected = i == state.playlistCurrentIndex;
      final marker = isCurrentlySelected ? '🎯' : '  ';
      debugPrint(
          '$marker [$i] ${music.title ?? 'Unknown'} - ${music.lyricist ?? 'Unknown'}');
    }

    // Upcoming songs
    final upcomingSongs = getUpcomingSongs(count: 5);
    debugPrint('\n⏭️ Upcoming Songs (${upcomingSongs.length} items):');
    for (int i = 0; i < upcomingSongs.length; i++) {
      final music = upcomingSongs[i];
      debugPrint(
          '  [+${i + 1}] ${music.title ?? 'Unknown'} - ${music.lyricist ?? 'Unknown'}');
    }

    debugPrint('=== END PLAYLIST DEBUG ===\n');
  }

  /// Enables or disables shuffle mode
  Future<void> setShuffleMode(bool enabled) async {
    await _audioPlayer.setShuffleModeEnabled(enabled);

    safeEmit(
      state.copyWith(
        isShuffleEnabled: enabled,
      ),
    );

    SdmToast.show(
      enabled ? 'Shuffle enabled' : 'Shuffle disabled',
    );
  }

  Future<void> musicPlayerListeners() async {
    await clearCache();

    _audioPlayer.playbackEventStream.listen(
      (playbackEvent) {
        updateCurrentMusic();
      },
      onError: (Object e, StackTrace stackTrace) {
        SdmToast.show(
          e.toString(),
        );
      },
    );

    _audioPlayer.playerStateStream.listen(
      (playerState) async {
        final playing = playerState.playing;
        final processingState = playerState.processingState;
        switch (processingState) {
          case ProcessingState.loading:
          case ProcessingState.buffering:
            safeEmit(
              state.copyWith(
                playerButtonState: PlayerButtonState.loading,
              ),
            );
            updateCurrentMusic();
            break;

          case ProcessingState.completed:
            await onSongComplete();
            break;

          case ProcessingState.ready:
            updateCurrentMusic();
            safeEmit(
              state.copyWith(
                playerButtonState: playing
                    ? PlayerButtonState.playing
                    : PlayerButtonState.paused,
              ),
            );
            break;

          default:
            safeEmit(
              state.copyWith(
                playlistCurrentIndex: _audioPlayer.currentIndex ?? 0,
                playerButtonState: PlayerButtonState.paused,
              ),
            );
        }
      },
      onError: (Object e, StackTrace stackTrace) {
        SdmToast.show(
          e.toString(),
        );
      },
    );

    _audioPlayer.positionStream.listen(
      (position) {
        safeEmit(
          state.copyWith(
            positionDuration: position,
          ),
        );
      },
      onError: (Object e, StackTrace stackTrace) {
        SdmToast.show(
          e.toString(),
        );
      },
    );

    _audioPlayer.durationStream.listen(
      (totalDuration) {
        safeEmit(
          state.copyWith(
            totalDuration: totalDuration,
          ),
        );
      },
      onError: (Object e, StackTrace stackTrace) {
        SdmToast.show(
          e.toString(),
        );
      },
    );

    _audioPlayer.bufferedPositionStream.listen(
      (bufferedPosition) {
        safeEmit(
          state.copyWith(
            bufferedDuration: bufferedPosition,
          ),
        );
      },
      onError: (Object e, StackTrace stackTrace) {
        SdmToast.show(
          e.toString(),
        );
      },
    );
  }

  Future<void> onSongComplete() async {
    if (state.loopMode == LoopModeEnum.once) {
      await pause();
      safeEmit(
        state.copyWith(
          playerButtonState: PlayerButtonState.paused,
        ),
      );
      return;
    }

    if ((state.playlistCurrentIndex) >= state.autoPlayLimit) {
      SdmToast.show(
        'Auto play limit reached',
      );
    } else {
      await seekToNext();
    }
    updateCurrentMusic();
    safeEmit(
      state.copyWith(
        playerButtonState: PlayerButtonState.paused,
      ),
    );
  }

  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  Future<void> seekToNext({bool force = false}) async {
    try {
      if (state.loopMode == LoopModeEnum.loop && !force) {
        return;
      }

      await switchLoopMode(
        loopMode: LoopModeEnum.playlist,
      );

      // Ensure we have enough songs in the queue before seeking
      await _maintainPlaylistQueue();

      // Log state before seeking
      debugPrint(
          '🔄 SeekToNext called - hasNext: ${_audioPlayer.hasNext}, currentIndex: ${state.playlistCurrentIndex}');

      // Check if we can seek to next
      final hasNext = _audioPlayer.hasNext;
      if (hasNext) {
        await _audioPlayer.seekToNext();
        debugPrint('✅ Seeked to next song successfully');
        logCurrentPlaylistItems();
      } else {
        // If no next song in audio player, but we have more in our playlist
        final currentMusicIndex = state.playlistCurrentIndex;
        final playlistLength = state.playlistChildren?.length ?? 0;

        if (currentMusicIndex + 1 < playlistLength) {
          debugPrint('🔄 Adding next song to queue and seeking...');
          // Add the next song and then seek to it
          await addNextSongToQueue();
          if (_audioPlayer.hasNext) {
            await _audioPlayer.seekToNext();
            debugPrint('✅ Added song and seeked successfully');
            logCurrentPlaylistItems();
          }
        } else {
          // No more songs available
          debugPrint('❌ No more songs in playlist');
          SdmToast.show('No more songs in playlist');
        }
      }
    } catch (e) {
      SdmToast.show('Error seeking to next song: ${e.toString()}');
    }
  }

  Future<void> switchLoopMode({
    LoopModeEnum? loopMode,
    bool setByUser = false,
  }) async {
    if (loopMode != null) {
      switch (loopMode) {
        case LoopModeEnum.once:
          await _audioPlayer.setLoopMode(LoopMode.off);
          safeEmit(
            state.copyWith(
              loopMode: LoopModeEnum.once,
            ),
          );
          if (setByUser) {
            SdmToast.show(
              'Will stop after current song ends',
            );
          }
          break;
        case LoopModeEnum.playlist:
          await _audioPlayer.setLoopMode(LoopMode.off);
          safeEmit(
            state.copyWith(
              loopMode: LoopModeEnum.playlist,
            ),
          );
          if (setByUser) {
            SdmToast.show(
              'Will play next song after current song ends',
            );
          }
          break;
        case LoopModeEnum.loop:
          await _audioPlayer.setLoopMode(LoopMode.one);

          safeEmit(
            state.copyWith(
              loopMode: LoopModeEnum.loop,
            ),
          );
          if (setByUser) {
            SdmToast.show(
              'Looping current song',
            );
          }
          break;
      }

      return;
    }

    switch (state.loopMode) {
      case LoopModeEnum.once:
        await switchLoopMode(
          loopMode: LoopModeEnum.loop,
        );
        break;
      case LoopModeEnum.playlist:
        await switchLoopMode(
          loopMode: LoopModeEnum.once,
        );
        break;
      case LoopModeEnum.loop:
        await switchLoopMode(
          loopMode: LoopModeEnum.playlist,
        );
        break;
    }
  }

  Future<void> seekToPrevious() async {
    await _audioPlayer.seekToPrevious();
  }

  Future<void> play() async {
    await _audioPlayer.play();
  }

  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  Future<void> setPlaybackSpeed(double speed) async {
    SdmToast.show(
      'Playback speed set to $speed',
    );
    await _audioPlayer.setSpeed(speed);

    safeEmit(
      state.copyWith(
        playbackSpeed: speed,
      ),
    );
  }

  Future<void> stop() async {
    await _audioPlayer.stop();
    safeEmit(
      state.copyWith(
        playerButtonState: PlayerButtonState.paused,
        currentMusic: () => null,
        playlistCurrentIndex: 0,
        queueSize: 0,
        initialStartIndex: -1,
      ),
    );
  }

  /// Clears the current playlist and reinitializes with new music list
  Future<void> reinitializePlaylist({
    required List<MusicModel> musicList,
    int index = 0,
    DocumentSnapshot? lastVisibleDocumentSnapshot,
    IndexEnum? indexEnum,
    int limit = 10,
  }) async {
    try {
      // Stop current playback
      await _audioPlayer.stop();

      // Clear the current playlist
      await _audioPlayer.setAudioSources([]);

      // Reinitialize with new playlist
      await initializeAudioPlayer(
        musicList: musicList,
        index: index,
        lastVisibleDocumentSnapshot: lastVisibleDocumentSnapshot,
        indexEnum: indexEnum,
        limit: limit,
      );
    } catch (e) {
      SdmToast.show(
        'Error reinitializing playlist: ${e.toString()}',
      );
    }
  }

  /// Gets the next few songs in the queue for UI display
  List<MusicModel> getUpcomingSongs({int count = 3}) {
    final List<MusicModel> upcomingSongs = [];
    final currentIndex = state.playlistCurrentIndex;
    final playlistChildren = state.playlistChildren ?? [];

    for (int i = 1; i <= count; i++) {
      final nextIndex = currentIndex + i;
      if (nextIndex < playlistChildren.length) {
        upcomingSongs.add(playlistChildren[nextIndex]);
      }
    }

    return upcomingSongs;
  }

  // Future<void> reinitialize() async {
  //   _audioPlayer.dispose();
  //   _audioPlayer = AudioPlayer();
  //   await initializeAudioPlayer(
  //     musicList: state.playlistChildren,
  //     indexEnum: state.indexEnum,
  //     index: state.playlistCurrentIndex,
  //   );
  // }

  @override
  Future<void> close() {
    _audioPlayer.dispose();
    return super.close();
  }
}
