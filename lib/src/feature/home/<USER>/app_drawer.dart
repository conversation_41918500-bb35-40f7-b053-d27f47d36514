import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/feature/feature.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Builder(builder: (context) {
      final userSessionState = context.watch<UserSessionCubit>().state;
      final purchasesState = context.watch<PurchasesCubit>().state;

      return SizedBox(
        width: 0.6.sw,
        child: Theme(
          data: Theme.of(context).copyWith(
            canvasColor: SdmPalette.white.withValues(
              alpha: 0.89,
            ),
          ),
          child: Drawer(
            child: Padding(
              padding: const EdgeInsets.only(left: 8).w,
              child: SafeArea(
                child: ListView(
                  physics: const ClampingScrollPhysics(),
                  padding: EdgeInsets.zero,
                  children: <Widget>[
                    DrawerListTile(
                      title: 'Profile',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.profile,
                        );
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              ProfileClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'About Us',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.aboutUs,
                        );
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              AboutUsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Music',
                      onTap: () async {
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              MusicClickEvent(),
                            );
                        if (purchasesState.hasMusicPremium ||
                            (userSessionState.userProfile?.isAdmin ?? false) ||
                            FlavorConfig.isDevelopment()) {
                          Navigator.popAndPushNamed(
                            context,
                            SdmRouter.musicList,
                          );
                          return;
                        }

                        if (context.mounted) {
                          context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                                MusicPaywallSheetOpenEvent(),
                              );
                          await showModalBottomSheet(
                            useRootNavigator: true,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(
                                top: const Radius.circular(10.0).r,
                              ),
                            ),
                            constraints: BoxConstraints(
                              maxWidth: 0.9.sw,
                            ),
                            context: context,
                            builder: (context) {
                              return const Paywall();
                            },
                          );
                        }
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Videos',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.videos,
                        );
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              VideosClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Events',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.calendarEvents,
                        );
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              CalendarClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Blogs',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.blogs,
                        );

                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              BlogsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Publications',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.publications,
                        );

                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              PublicationsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Get your certificates!',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.enterEmail,
                        );
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              CertificatesClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Contact Us',
                      onTap: () {
                        Navigator.popAndPushNamed(
                          context,
                          SdmRouter.contactUs,
                        );

                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              ContactUsClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    DrawerListTile(
                      title: 'Info',
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return const DeviceInfoDialog();
                          },
                        );
                        context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
                              InfoClickEvent(),
                            );
                      },
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    if (userSessionState.userProfile?.isAdmin ?? false)
                      DrawerListTile(
                        title: 'Admin',
                        onTap: () {
                          Navigator.popAndPushNamed(
                            context,
                            SdmRouter.adminHome,
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
