import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shridattmandir/src/core/core.dart' show Assets, SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        AuthCubit,
        AuthState,
        AnalyticsCubit,
        VerifyOtpClickEvent,
        ResendOtpClickEvent,
        PurchasesCubit;
import 'package:shridattmandir/src/shared/shared.dart' show SdmPrimaryCta;

class PhoneOtpScreen extends StatelessWidget {
  const PhoneOtpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) => previous.user != current.user,
      listener: (context, state) async {
        debugPrint('OTP Screen: Auth state changed. User: ${state.user?.uid}');
        if (state.user != null) {
          debugPrint('OTP Screen: User authenticated, navigating to home');
          // User authenticated successfully, navigate to home
          await Future.delayed(const Duration(
              milliseconds: 500)); // Small delay for UserSessionListener
          if (context.mounted) {
            Navigator.pushNamedAndRemoveUntil(
              context,
              '/home',
              (route) => false,
            );
          }
        }
      },
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: const Text(
                'Verify',
              ),
              foregroundColor: SdmPalette.black,
            ),
            body: ListView(
              physics: const ClampingScrollPhysics(),
              children: [
                SizedBox(
                  height: 100.h,
                ),
                Assets.sdmImages.otpIllustration.image(
                  height: 180.h,
                ),
                SizedBox(
                  height: 12.h,
                ),
                Text(
                  'Verification',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 26.0).w,
                  child: Text(
                    'Enter your OTP code number',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: SdmPalette.textColorGrey,
                    ),
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40.0).w,
                  child: Pinput(
                    length: 6,
                    controller: context.read<AuthCubit>().smsCodeController,
                    keyboardType: TextInputType.number,
                    defaultPinTheme: PinTheme(
                      height: 44.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                        boxShadow: const [
                          BoxShadow(
                            color: SdmPalette.black29,
                            blurRadius: 4,
                            offset: Offset(0, 4),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(
                        minWidth: 130.w,
                        maxWidth: 130.w,
                      ),
                      child: SdmPrimaryCta(
                        child: state.loading
                            ? const CircularProgressIndicator.adaptive()
                            : Text(
                                'Verify',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: SdmPalette.white,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                        onPressed: () async {
                          context
                              .read<AnalyticsCubit>()
                              .onTrackAnalyticsEvent(VerifyOtpClickEvent());
                          if (!(await Purchases.isConfigured)) {
                            if (context.mounted) {
                              await context
                                  .read<PurchasesCubit>()
                                  .initPlatformState();
                            }
                          }
                          if (context.mounted) {
                            await context
                                .read<AuthCubit>()
                                .signInWithCredential();
                          }

                          // Navigation will be handled by UserSessionListener
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 22.h,
                ),
                Column(
                  children: [
                    Text(
                      'Didn’t receive any code?',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: SdmPalette.textColorGrey,
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        context
                            .read<AnalyticsCubit>()
                            .onTrackAnalyticsEvent(ResendOtpClickEvent());
                        await context.read<AuthCubit>().verifyPhoneNumber(
                              forceResendingToken: state.forceResendingToken,
                              resendOtp: true,
                            );
                      },
                      child: Text(
                        'Resend New Code',
                        style: TextStyle(
                          color: SdmPalette.lightRed,
                          fontWeight: FontWeight.w600,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
