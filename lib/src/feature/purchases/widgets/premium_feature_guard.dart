import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        FlavorConfig,
        PaywallDisplayMode,
        PaywallManager,
        PurchasesCubit,
        PurchasesState,
        UserSessionCubit,
        UserSessionState;

/// A widget that guards premium features and shows paywall when needed
/// This provides a consistent way to protect premium content across the app
class PremiumFeatureGuard extends StatelessWidget {
  const PremiumFeatureGuard({
    super.key,
    required this.child,
    this.fallback,
    this.paywallMode = PaywallDisplayMode.modal,
    this.showPaywallOnTap = true,
    this.requireMusicPremium = true,
    this.allowAdminAccess = true,
  });

  /// The widget to show when user has premium access
  final Widget child;

  /// Optional widget to show when user doesn't have premium access
  /// If null, will show a default "Premium Required" message
  final Widget? fallback;

  /// How to display the paywall when user taps on restricted content
  final PaywallDisplayMode paywallMode;

  /// Whether to show paywall when user taps on the fallback widget
  final bool showPaywallOnTap;

  /// Whether this feature requires music premium subscription
  final bool requireMusicPremium;

  /// Whether to allow admin users to access this feature
  final bool allowAdminAccess;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PurchasesCubit, PurchasesState>(
      builder: (context, purchasesState) {
        return BlocBuilder<UserSessionCubit, UserSessionState>(
          builder: (context, userSessionState) {
            final hasAccess = _hasAccess(
              purchasesState: purchasesState,
              userSessionState: userSessionState,
            );

            if (hasAccess) {
              return child;
            }

            return _buildFallback(context);
          },
        );
      },
    );
  }

  bool _hasAccess({
    required PurchasesState purchasesState,
    required UserSessionState userSessionState,
  }) {
    // Always allow access in development mode
    if (FlavorConfig.isDevelopment()) return true;

    // Allow admin access if enabled
    if (allowAdminAccess && (userSessionState.userProfile?.isAdmin ?? false)) {
      return true;
    }

    // Check music premium subscription if required
    if (requireMusicPremium) {
      return purchasesState.hasMusicPremium;
    }

    return false;
  }

  Widget _buildFallback(BuildContext context) {
    final fallbackWidget = fallback ?? _buildDefaultFallback(context);

    if (!showPaywallOnTap) {
      return fallbackWidget;
    }

    return GestureDetector(
      onTap: () => _showPaywall(context),
      child: fallbackWidget,
    );
  }

  Widget _buildDefaultFallback(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock_outline,
            size: 48,
            color: Colors.grey.shade600,
          ),
          const SizedBox(height: 8),
          Text(
            'Premium Feature',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            'Subscribe to access this feature',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
            textAlign: TextAlign.center,
          ),
          if (showPaywallOnTap) ...[
            const SizedBox(height: 8),
            Text(
              'Tap to subscribe',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ],
      ),
    );
  }

  void _showPaywall(BuildContext context) {
    PaywallManager.showPaywall(
      context: context,
      mode: paywallMode,
    );
  }
}

/// A simplified version for quick premium checks
class PremiumGate extends StatelessWidget {
  const PremiumGate({
    super.key,
    required this.child,
    this.onPremiumRequired,
  });

  final Widget child;
  final VoidCallback? onPremiumRequired;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PurchasesCubit, PurchasesState>(
      builder: (context, purchasesState) {
        return BlocBuilder<UserSessionCubit, UserSessionState>(
          builder: (context, userSessionState) {
            final hasAccess = FlavorConfig.isDevelopment() ||
                (userSessionState.userProfile?.isAdmin ?? false) ||
                purchasesState.hasMusicPremium;

            if (hasAccess) {
              return child;
            }

            return GestureDetector(
              onTap: () {
                onPremiumRequired?.call();
                PaywallManager.showPaywall(context: context);
              },
              child: child,
            );
          },
        );
      },
    );
  }
}
