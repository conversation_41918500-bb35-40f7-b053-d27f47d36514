import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show Assets, SdmPalette;
import 'package:shridattmandir/src/feature/feature.dart'
    show AnalyticsCubit, MusicPaywallSheetOpenEvent, PaywallManager;

/// A beautiful full-screen widget that shows when music premium is required
/// This provides a better user experience than just showing a paywall modal
class MusicPremiumRequiredScreen extends StatelessWidget {
  const MusicPremiumRequiredScreen({
    super.key,
    this.title = 'Premium Music Access',
    this.subtitle = 'Unlock unlimited access to our spiritual music collection',
  });

  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SdmPalette.white,
      appBar: AppBar(
        title: const Text(
          'Music',
          style: TextStyle(
            color: SdmPalette.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: SdmPalette.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: SdmPalette.black),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Illustration
              Assets.sdmImages.subscribeIllustration.image(
                height: 200.h,
                fit: BoxFit.contain,
              ),

              SizedBox(height: 40.h),

              // Title
              Text(
                title,
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: SdmPalette.black,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 16.h),

              // Subtitle
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: SdmPalette.black54,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 40.h),

              // Features list
              _buildFeaturesList(),

              SizedBox(height: 40.h),

              // Subscribe button
              SizedBox(
                width: double.infinity,
                height: 50.h,
                child: ElevatedButton(
                  onPressed: () => _showPaywall(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: SdmPalette.primary,
                    foregroundColor: SdmPalette.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    'Subscribe to Premium',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              SizedBox(height: 16.h),

              // Learn more button
              TextButton(
                onPressed: () => _showPaywall(context),
                child: Text(
                  'Learn More About Premium',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: SdmPalette.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      'Unlimited access to all spiritual music',
      'High-quality audio streaming',
      'Offline listening capability',
      'Ad-free music experience',
      'Exclusive devotional content',
    ];

    return Column(
      children: features.map((feature) => _buildFeatureItem(feature)).toList(),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: SdmPalette.primary,
            size: 20.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              feature,
              style: TextStyle(
                fontSize: 14.sp,
                color: SdmPalette.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showPaywall(BuildContext context) {
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
          MusicPaywallSheetOpenEvent(),
        );

    PaywallManager.showPaywall(context: context);
  }
}
