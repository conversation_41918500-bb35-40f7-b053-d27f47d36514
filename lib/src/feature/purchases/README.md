# Subscription Paywall Implementation Guide

This document outlines the consistent and maintainable approach for implementing subscription paywalls across the app.

## Architecture Overview

The subscription system follows a centralized approach with three main components:

1. **SubscriptionService** - Centralized subscription state management
2. **PaywallManager** - Consistent paywall display logic
3. **PremiumFeatureGuard** - Widget-based premium feature protection

## Components

### 1. SubscriptionService

Provides a consistent interface for subscription checks across the app.

```dart
// Get subscription service from PurchasesCubit
final subscriptionService = SubscriptionService(
  purchasesCubit: context.read<PurchasesCubit>(),
);

// Check subscription status
if (subscriptionService.hasMusicPremium) {
  // User has premium access
}

// Check with admin privileges
if (subscriptionService.hasMusicAccess(isAdmin: user.isAdmin)) {
  // User has access (premium or admin)
}
```

### 2. PaywallManager

Centralized paywall display with consistent UI and analytics.

```dart
// Show paywall as modal (recommended)
await PaywallManager.showPaywallModal(context: context);

// Navigate to paywall
await PaywallManager.navigateToPaywall(context: context);

// Smart paywall display
await PaywallManager.showPaywall(
  context: context,
  mode: PaywallDisplayMode.modal,
);
```

### 3. PremiumFeatureGuard

Widget-based approach for protecting premium content with multiple usage patterns.

```dart
// Full screen protection with custom fallback
PremiumFeatureGuard(
  requireMusicPremium: true,
  allowAdminAccess: true,
  paywallMode: PaywallDisplayMode.modal,
  child: MusicPlayerScreen(),
  fallback: MusicPremiumRequiredScreen(
    title: 'Premium Music Player',
    subtitle: 'Subscribe to enjoy unlimited music streaming',
  ),
)

// Hide widget for non-premium users
PremiumFeatureGuard(
  showPaywallOnTap: false,
  fallback: const SizedBox.shrink(),
  child: MusicNowPlayingBottomBar(),
)

// Simple gate with analytics
PremiumGate(
  onPremiumRequired: () {
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
      MusicPlayEvent(musicName: 'Song Name', id: 'song_id'),
    );
  },
  child: MusicListTile(),
)

// Custom premium check behavior
PremiumFeatureGuard(
  requireMusicPremium: true,
  allowAdminAccess: false, // Only premium users, not admins
  paywallMode: PaywallDisplayMode.navigation,
  showPaywallOnTap: true,
  child: ExclusivePremiumContent(),
)
```

## Usage Patterns

### 1. Navigation-Based Premium Checks

```dart
// In drawer or navigation
PremiumGate(
  onPremiumRequired: () {
    // Track analytics
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
      MusicClickEvent(),
    );
  },
  child: DrawerListTile(
    title: 'Music',
    onTap: () => Navigator.pushNamed(context, SdmRouter.musicList),
  ),
)
```

### 2. Content-Based Premium Protection

```dart
// Protect entire screens or widgets
PremiumFeatureGuard(
  child: MusicPlayerScreen(),
  fallback: SubscriptionRequiredWidget(),
)
```

### 3. Conditional Feature Access

```dart
// In build methods
BlocBuilder<PurchasesCubit, PurchasesState>(
  builder: (context, state) {
    final subscriptionService = SubscriptionService(
      purchasesCubit: context.read<PurchasesCubit>(),
    );

    if (subscriptionService.hasMusicAccess(isAdmin: user.isAdmin)) {
      return MusicFeatures();
    }

    return GestureDetector(
      onTap: () => PaywallManager.showPaywall(context: context),
      child: PremiumRequiredWidget(),
    );
  },
)
```

## Best Practices

### ✅ DO

- Use `SubscriptionService` for all subscription checks
- Use `PaywallManager` for consistent paywall display
- Use `PremiumFeatureGuard` for widget-based protection
- Track analytics when showing paywalls
- Handle development mode appropriately
- Use the cubit pattern for state management

### ❌ DON'T

- Create new `PurchasesRepository()` instances directly
- Bypass the cubit for subscription checks
- Implement custom paywall display logic
- Forget to track analytics
- Mix subscription logic with business logic

## Migration Guide

### From Direct Repository Usage

```dart
// OLD - Direct repository usage
final customerInfo = await PurchasesRepository().getCustomerInfo();
final hasPremium = customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;

// NEW - Use SubscriptionService
final subscriptionService = SubscriptionService(
  purchasesCubit: context.read<PurchasesCubit>(),
);
final hasPremium = subscriptionService.hasMusicPremium;
```

### From Custom Paywall Display

```dart
// OLD - Custom modal
showModalBottomSheet(
  context: context,
  builder: (context) => const Paywall(),
);

// NEW - Use PaywallManager
PaywallManager.showPaywallModal(context: context);
```

### From Manual Premium Checks

```dart
// OLD - Manual checks
if (purchasesState.hasMusicPremium || user.isAdmin || FlavorConfig.isDevelopment()) {
  return MusicWidget();
}
return PremiumRequiredWidget();

// NEW - Use PremiumFeatureGuard
PremiumFeatureGuard(
  child: MusicWidget(),
  fallback: PremiumRequiredWidget(),
)
```

## Error Handling

The new system includes built-in error handling:

- Development mode bypass for testing
- Graceful fallbacks for network errors
- Consistent error messaging
- Analytics tracking for error scenarios

## Testing

```dart
// Mock subscription service for tests
final mockSubscriptionService = MockSubscriptionService();
when(mockSubscriptionService.hasMusicPremium).thenReturn(true);

// Test premium feature guards
testWidgets('shows premium content when subscribed', (tester) async {
  // Setup mock cubit with premium access
  // Test widget behavior
});
```

This approach ensures consistency, maintainability, and reliability across the entire subscription system.
