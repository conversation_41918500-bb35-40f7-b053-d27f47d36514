import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmRouter;
import 'package:shridattmandir/src/feature/feature.dart'
    show AnalyticsCubit, MusicPaywallSheetOpenEvent, Paywall;

/// Centralized manager for displaying paywalls consistently across the app
/// This ensures a uniform user experience and consistent analytics tracking
class PaywallManager {
  PaywallManager._();

  static final PaywallManager _instance = PaywallManager._();
  static PaywallManager get instance => _instance;

  /// Shows the paywall as a modal bottom sheet
  /// This is the preferred method for displaying the paywall
  static Future<void> showPaywallModal({
    required BuildContext context,
    bool trackAnalytics = true,
  }) async {
    if (trackAnalytics) {
      context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
            MusicPaywallSheetOpenEvent(),
          );
    }

    await showModalBottomSheet(
      context: context,
      useRootNavigator: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: const Radius.circular(10.0).r,
        ),
      ),
      constraints: BoxConstraints(
        maxWidth: 0.9.sw,
        maxHeight: 0.85.sh,
      ),
      builder: (context) => const Paywall(),
    );
  }

  /// Navigates to the paywall screen
  /// Use this when you need to navigate away from the current screen
  static Future<void> navigateToPaywall({
    required BuildContext context,
    bool trackAnalytics = true,
  }) async {
    if (trackAnalytics) {
      context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
            MusicPaywallSheetOpenEvent(),
          );
    }

    Navigator.pushNamed(context, SdmRouter.paywall);
  }

  /// Shows paywall with navigation replacement
  /// Use this when you want to replace the current route
  static Future<void> showPaywallWithReplacement({
    required BuildContext context,
    bool trackAnalytics = true,
  }) async {
    if (trackAnalytics) {
      context.read<AnalyticsCubit>().onTrackAnalyticsEvent(
            MusicPaywallSheetOpenEvent(),
          );
    }

    Navigator.pushReplacementNamed(context, SdmRouter.paywall);
  }

  /// Determines the best paywall display method based on context
  /// This provides a smart default for showing the paywall
  static Future<void> showPaywall({
    required BuildContext context,
    PaywallDisplayMode mode = PaywallDisplayMode.modal,
    bool trackAnalytics = true,
  }) async {
    switch (mode) {
      case PaywallDisplayMode.modal:
        await showPaywallModal(
          context: context,
          trackAnalytics: trackAnalytics,
        );
        break;
      case PaywallDisplayMode.navigation:
        await navigateToPaywall(
          context: context,
          trackAnalytics: trackAnalytics,
        );
        break;
      case PaywallDisplayMode.replacement:
        await showPaywallWithReplacement(
          context: context,
          trackAnalytics: trackAnalytics,
        );
        break;
    }
  }
}

/// Enum defining different ways to display the paywall
enum PaywallDisplayMode {
  /// Show as a modal bottom sheet (recommended)
  modal,
  
  /// Navigate to paywall screen
  navigation,
  
  /// Replace current screen with paywall
  replacement,
}
