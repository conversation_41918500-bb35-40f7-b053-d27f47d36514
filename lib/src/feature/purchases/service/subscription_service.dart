import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show FlavorConfig, PurchasesCubit, PurchasesState;

/// Centralized service for handling subscription-related operations
/// This service provides a consistent interface for subscription checks
/// and premium feature access across the entire application.
class SubscriptionService {
  SubscriptionService({
    required PurchasesCubit purchasesCubit,
  }) : _purchasesCubit = purchasesCubit;

  final PurchasesCubit _purchasesCubit;

  /// Checks if the user has an active music premium subscription
  /// Returns true in development mode for testing purposes
  bool get hasMusicPremium {
    if (FlavorConfig.isDevelopment()) return true;
    return _purchasesCubit.state.hasMusicPremium;
  }

  /// Checks if the user has access to premium music features
  /// This includes subscription status and admin privileges
  bool hasMusicAccess({bool? isAdmin}) {
    if (FlavorConfig.isDevelopment()) return true;
    if (isAdmin == true) return true;
    return hasMusicPremium;
  }

  /// Gets the current subscription state
  PurchasesState get subscriptionState => _purchasesCubit.state;

  /// Refreshes the subscription status from the server
  Future<void> refreshSubscriptionStatus() async {
    await _purchasesCubit.getSubscriptionStatus();
  }

  /// Initiates the purchase flow for subscription
  Future<void> startPurchaseFlow() async {
    await _purchasesCubit.purchaseFlow();
  }

  /// Restores previous purchases
  Future<void> restorePurchases() async {
    await _purchasesCubit.restorePurchase();
  }

  /// Logs out from the subscription service
  Future<void> logout() async {
    await _purchasesCubit.logOut();
  }

  /// Stream of subscription state changes
  Stream<PurchasesState> get subscriptionStateStream => _purchasesCubit.stream;

  /// Gets FCM token for push notifications
  Future<String?> getFcmToken() async {
    return await FirebaseMessaging.instance.getToken();
  }

  /// Gets both FCM token and music subscription status
  /// This replaces the deprecated fcmAndMusicSubscribed method
  Future<List<Object?>> getFcmTokenAndSubscriptionStatus() async {
    try {
      return await Future.wait([
        getFcmToken(),
        Future.value(hasMusicPremium),
      ]);
    } catch (e) {
      debugPrint('Error getting FCM token and subscription status: $e');
      return [null, false];
    }
  }

  /// Debug method to print current subscription status
  void debugSubscriptionStatus() {
    if (kDebugMode) {
      debugPrint('=== SUBSCRIPTION STATUS ===');
      debugPrint('Has Music Premium: $hasMusicPremium');
      debugPrint(
          'Customer ID: ${subscriptionState.customerInfo?.originalAppUserId}');
      debugPrint('Loading: ${subscriptionState.loading}');
      debugPrint('===========================');
    }
  }
}
