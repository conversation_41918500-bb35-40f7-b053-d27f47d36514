import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shridattmandir/src/feature/feature.dart' show UserModel;
import 'package:shridattmandir/src/feature/purchases/purchases.dart';

class ProfileRepository {
  ProfileRepository(
    FirebaseFirestore firestore,
  ) : _firestore = firestore;

  final FirebaseFirestore _firestore;

  static const String _collectionName = 'users';

  Future<String?> getFcmToken() async {
    return await FirebaseMessaging.instance.getToken();
  }

  Future<bool> musicSubscribed() async {
    final customerInfo = await PurchasesRepository().getCustomerInfo();
    return customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;
  }

  Future<List<Object?>> fcmAndMusicSubscribed() async {
    return await Future.wait(
      [
        getFcmToken(),
        musicSubscribed(),
      ],
    );
  }

  Future<void> setUserDataToFirestore({
    required UserModel user,
  }) async {
    final result = await fcmAndMusicSubscribed();

    await _firestore.collection(_collectionName).doc(user.uid).set(
          user
              .copyWith(
                fcmToken: result[0] is String? ? result[0] as String? : null,
                hasMusicPremium: result[1] is bool? ? result[1] as bool? : null,
              )
              .toJson(),
        );
  }

  Future<void> updateUserDataToFirestore({
    required UserModel user,
  }) async {
    final result = await fcmAndMusicSubscribed();

    await _firestore.collection(_collectionName).doc(user.uid).update(
          user
              .copyWith(
                fcmToken: result[0] is String? ? result[0] as String? : null,
                hasMusicPremium: result[1] is bool? ? result[1] as bool? : null,
              )
              .toJson(),
        );
  }

  Future<void> deleteUserDataFromFirestore({required String uid}) async {
    await _firestore
        .collection(_collectionName)
        .doc(
          uid,
        )
        .delete();
  }

  Future<UserModel?> getUserDataFromFirestore({required String uid}) async {
    final DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
        await _firestore
            .collection(_collectionName)
            .doc(
              uid,
            )
            .get();

    if (documentSnapshot.exists) {
      return UserModel.fromJson(
        documentSnapshot.data()!,
      );
    } else {
      return null;
    }
  }
}
