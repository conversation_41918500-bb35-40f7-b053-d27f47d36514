import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:shridattmandir/src/feature/purchases/purchases.dart';
import 'package:shridattmandir/src/feature/user/models/user_model.dart';

/// Repository for managing user data in Firestore
/// Handles user profile operations and related data
class UserRepository {
  UserRepository(
    FirebaseFirestore firestore,
  ) : _firestore = firestore;

  final FirebaseFirestore _firestore;

  static const String _collectionName = 'users';

  /// Gets FCM token for push notifications
  Future<String?> getFcmToken() async {
    return await FirebaseMessaging.instance.getToken();
  }

  /// Checks if user has active music subscription
  /// @deprecated Use SubscriptionService instead for consistent subscription checks
  @Deprecated('Use SubscriptionService.hasMusicPremium instead')
  Future<bool> hasMusicSubscription() async {
    try {
      final customerInfo = await PurchasesRepository().getCustomerInfo();
      return customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;
    } catch (e) {
      debugPrint('Error checking music subscription: $e');
      return false;
    }
  }

  /// Gets both FCM token and music subscription status
  Future<List<Object?>> _getAdditionalUserData() async {
    try {
      return await Future.wait(
        [
          getFcmToken(),
          hasMusicSubscription(),
        ],
      );
    } catch (e) {
      debugPrint('Error getting additional user data: $e');
      return [null, false];
    }
  }

  /// Creates a new user document in Firestore
  Future<void> createUser({
    required UserModel user,
  }) async {
    final additionalData = await _getAdditionalUserData();

    await _firestore.collection(_collectionName).doc(user.uid).set(
          user
              .copyWith(
                fcmToken: additionalData[0] is String?
                    ? additionalData[0] as String?
                    : null,
                hasMusicPremium: additionalData[1] is bool?
                    ? additionalData[1] as bool?
                    : null,
              )
              .toJson(),
        );
  }

  /// Updates an existing user document in Firestore
  Future<void> updateUser({
    required UserModel user,
  }) async {
    final additionalData = await _getAdditionalUserData();

    await _firestore.collection(_collectionName).doc(user.uid).update(
          user
              .copyWith(
                fcmToken: additionalData[0] is String?
                    ? additionalData[0] as String?
                    : null,
                hasMusicPremium: additionalData[1] is bool?
                    ? additionalData[1] as bool?
                    : null,
              )
              .toJson(),
        );
  }

  /// Deletes a user document from Firestore
  Future<void> deleteUser({required String uid}) async {
    await _firestore.collection(_collectionName).doc(uid).delete();
  }

  /// Gets user data from Firestore by UID
  Future<UserModel?> getUser({required String uid}) async {
    final DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
        await _firestore.collection(_collectionName).doc(uid).get();

    if (documentSnapshot.exists) {
      return UserModel.fromJson(
        documentSnapshot.data()!,
      );
    } else {
      return null;
    }
  }

  /// Updates user's music favorites
  Future<void> updateMusicFavorites({
    required String uid,
    required List<String> favorites,
  }) async {
    await _firestore
        .collection(_collectionName)
        .doc(uid)
        .update({'musicFavorites': favorites});
  }

  /// Updates user's FCM token
  Future<void> updateFcmToken({
    required String uid,
    required String fcmToken,
  }) async {
    await _firestore
        .collection(_collectionName)
        .doc(uid)
        .update({'fcmToken': fcmToken});
  }
}
